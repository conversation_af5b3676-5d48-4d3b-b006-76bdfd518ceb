import 'package:get/get.dart';
import '../models/movie_model.dart';
import '../services/movie_service.dart';
import '../services/firebase_movie_service.dart';

class MovieController extends GetxController {
  final MovieService _movieService = MovieService();
  final FirebaseMovieService _firebaseMovieService = FirebaseMovieService();

  final RxList<Movie> popularMovies = <Movie>[].obs;
  final RxList<Movie> upcomingMovies = <Movie>[].obs;
  final RxList<Movie> searchResults = <Movie>[].obs;
  final Rx<Movie?> selectedMovie = Rx<Movie?>(null);

  // Firebase movies
  final RxList<Movie> allMovies = <Movie>[].obs;
  final RxList<Movie> homeBannerMovies = <Movie>[].obs;
  final RxList<Movie> splashBannerMovies = <Movie>[].obs;
  final RxList<Movie> nowPlayingMovies = <Movie>[].obs;
  final RxList<Movie> upcomingFirebaseMovies = <Movie>[].obs;
  final RxList<Movie> endedMovies = <Movie>[].obs;

  final RxBool isLoadingPopular = false.obs;
  final RxBool isLoadingUpcoming = false.obs;
  final RxBool isLoadingSearch = false.obs;
  final RxBool isLoadingMovieDetails = false.obs;
  final RxBool isLoadingFirebaseMovies = false.obs;
  final RxBool isSubmitting = false.obs;

  final RxString errorMessage = ''.obs;
  final RxString searchQuery = ''.obs;

  @override
  void onInit() {
    super.onInit();
    fetchPopularMovies();
    fetchUpcomingMovies();
    fetchAllFirebaseMovies();
    fetchHomeBannerMovies();
    fetchSplashBannerMovies();
  }

  Future<void> fetchPopularMovies() async {
    try {
      isLoadingPopular.value = true;
      errorMessage.value = '';

      final movies = await _movieService.getPopularMovies();
      popularMovies.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to load popular movies: $e';
    } finally {
      isLoadingPopular.value = false;
    }
  }

  Future<void> fetchUpcomingMovies() async {
    try {
      isLoadingUpcoming.value = true;
      errorMessage.value = '';

      final movies = await _movieService.getUpcomingMovies();
      upcomingMovies.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to load upcoming movies: $e';
    } finally {
      isLoadingUpcoming.value = false;
    }
  }

  Future<void> searchMovies(String query) async {
    if (query.isEmpty) {
      searchResults.clear();
      return;
    }

    try {
      isLoadingSearch.value = true;
      errorMessage.value = '';
      searchQuery.value = query;

      final movies = await _movieService.searchMovies(query);
      searchResults.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to search movies: $e';
    } finally {
      isLoadingSearch.value = false;
    }
  }

  Future<Movie?> getMovieDetails(int movieId) async {
    try {
      isLoadingMovieDetails.value = true;
      errorMessage.value = '';

      final movie = await _movieService.getMovieDetails(movieId);
      selectedMovie.value = movie;
      return movie;
    } catch (e) {
      errorMessage.value = 'Failed to load movie details: $e';
      return null;
    } finally {
      isLoadingMovieDetails.value = false;
    }
  }

  void clearSearch() {
    searchResults.clear();
    searchQuery.value = '';
  }

  // Firebase Movie Methods
  Future<void> fetchAllFirebaseMovies() async {
    try {
      isLoadingFirebaseMovies.value = true;
      errorMessage.value = '';

      final movies = await _firebaseMovieService.getMovies();
      allMovies.value = movies;

      // Categorize movies by status
      nowPlayingMovies.value =
          movies.where((m) => m.status == MovieStatus.nowPlaying).toList();
      upcomingFirebaseMovies.value =
          movies.where((m) => m.status == MovieStatus.upcoming).toList();
      endedMovies.value =
          movies.where((m) => m.status == MovieStatus.ended).toList();
    } catch (e) {
      errorMessage.value = 'Failed to load movies: $e';
    } finally {
      isLoadingFirebaseMovies.value = false;
    }
  }

  Future<void> fetchHomeBannerMovies() async {
    try {
      errorMessage.value = '';
      final movies = await _firebaseMovieService.getHomeBannerMovies();
      homeBannerMovies.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to load home banner movies: $e';
    }
  }

  Future<void> fetchSplashBannerMovies() async {
    try {
      errorMessage.value = '';
      final movies = await _firebaseMovieService.getSplashBannerMovies();
      splashBannerMovies.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to load splash banner movies: $e';
    }
  }

  Future<void> searchFirebaseMovies(String query) async {
    if (query.isEmpty) {
      searchResults.clear();
      return;
    }

    try {
      isLoadingSearch.value = true;
      errorMessage.value = '';
      searchQuery.value = query;

      final movies = await _firebaseMovieService.searchMovies(query);
      searchResults.value = movies;
    } catch (e) {
      errorMessage.value = 'Failed to search movies: $e';
    } finally {
      isLoadingSearch.value = false;
    }
  }

  // Admin methods
  Future<bool> addMovie(Movie movie) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      await _firebaseMovieService.addMovie(movie);
      await fetchAllFirebaseMovies(); // Refresh the list
      return true;
    } catch (e) {
      errorMessage.value = 'Failed to add movie: $e';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  Future<bool> updateMovie(Movie movie) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      await _firebaseMovieService.updateMovie(movie);
      await fetchAllFirebaseMovies(); // Refresh the list

      // Refresh banner lists if banner status changed
      if (movie.isHomeBanner) {
        await fetchHomeBannerMovies();
      }
      if (movie.isSplashBanner) {
        await fetchSplashBannerMovies();
      }

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to update movie: $e';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  Future<bool> deleteMovie(String movieId) async {
    try {
      isSubmitting.value = true;
      errorMessage.value = '';

      await _firebaseMovieService.deleteMovie(movieId);
      await fetchAllFirebaseMovies(); // Refresh the list
      await fetchHomeBannerMovies(); // Refresh banner lists
      await fetchSplashBannerMovies();

      return true;
    } catch (e) {
      errorMessage.value = 'Failed to delete movie: $e';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  Future<bool> toggleHomeBanner(String movieId, bool isHomeBanner,
      {int? order}) async {
    try {
      errorMessage.value = '';
      await _firebaseMovieService.toggleHomeBanner(movieId, isHomeBanner,
          order: order);
      await fetchHomeBannerMovies();
      await fetchAllFirebaseMovies();
      return true;
    } catch (e) {
      errorMessage.value = 'Failed to toggle home banner: $e';
      return false;
    }
  }

  Future<bool> toggleSplashBanner(String movieId, bool isSplashBanner,
      {int? order}) async {
    try {
      errorMessage.value = '';
      await _firebaseMovieService.toggleSplashBanner(movieId, isSplashBanner,
          order: order);
      await fetchSplashBannerMovies();
      await fetchAllFirebaseMovies();
      return true;
    } catch (e) {
      errorMessage.value = 'Failed to toggle splash banner: $e';
      return false;
    }
  }
}
