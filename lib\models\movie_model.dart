import 'package:cloud_firestore/cloud_firestore.dart';

enum MovieStatus { nowPlaying, upcoming, ended }

extension MovieStatusExtension on MovieStatus {
  String get name {
    switch (this) {
      case MovieStatus.nowPlaying:
        return 'now_playing';
      case MovieStatus.upcoming:
        return 'upcoming';
      case MovieStatus.ended:
        return 'ended';
    }
  }

  static MovieStatus fromString(String? value) {
    switch (value) {
      case 'upcoming':
        return MovieStatus.upcoming;
      case 'ended':
        return MovieStatus.ended;
      default:
        return MovieStatus.nowPlaying;
    }
  }

  String get displayName {
    switch (this) {
      case MovieStatus.nowPlaying:
        return '<PERSON><PERSON>';
      case MovieStatus.upcoming:
        return 'Sắp <PERSON>u';
      case MovieStatus.ended:
        return 'Đã <PERSON>t <PERSON>húc';
    }
  }
}

class Movie {
  // Helper method to safely parse double values
  static double? _parseDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value);
    return null;
  }

  // Helper method to safely parse int values
  static int? _parseInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }

  final int id;
  final String title;
  final String? originalTitle;
  final String? subtitle;
  final String? overview;
  final String? posterPath;
  final String? backdropPath;
  final String? trailerUrl;
  final List<String> genres;
  final String? releaseDate;
  final int? runtime; // phút
  final double? voteAverage;
  final int? voteCount;
  final double? popularity;
  final String? ageRating;
  final String? language;
  final String? country;
  final String? director;
  final List<Cast>? cast;
  final MovieStatus status;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final bool isFavorite;
  final bool isHomeBanner;
  final bool isSplashBanner;
  final int? bannerOrder;

  Movie({
    required this.id,
    required this.title,
    this.originalTitle,
    this.subtitle,
    this.overview,
    this.posterPath,
    this.backdropPath,
    this.trailerUrl,
    this.genres = const [],
    this.releaseDate,
    this.runtime,
    this.voteAverage,
    this.voteCount,
    this.popularity,
    this.ageRating,
    this.language,
    this.country,
    this.director,
    this.cast,
    this.status = MovieStatus.nowPlaying,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.isFavorite = false,
    this.isHomeBanner = false,
    this.isSplashBanner = false,
    this.bannerOrder,
  });

  String get fullPosterPath => posterPath != null
      ? 'https://image.tmdb.org/t/p/w500$posterPath'
      : 'https://via.placeholder.com/500x750?text=No+Image';

  String get fullBackdropPath => backdropPath != null
      ? 'https://image.tmdb.org/t/p/original$backdropPath'
      : 'https://via.placeholder.com/1280x720?text=No+Image';

  String get genresString => genres.join(', ');

  String get rating =>
      voteAverage != null ? voteAverage!.toStringAsFixed(1) : 'N/A';

  String get year => releaseDate != null && releaseDate!.length >= 4
      ? releaseDate!.substring(0, 4)
      : '';

  factory Movie.fromJson(Map<String, dynamic> json, {bool isFavorite = false}) {
    List<String> genresList = [];
    if (json['genres'] != null) {
      genresList = (json['genres'] as List)
          .map((genre) => genre['name'] as String)
          .toList();
    } else if (json['genre_ids'] != null) {}

    List<Cast>? castList;
    if (json['credits'] != null && json['credits']['cast'] != null) {
      castList = (json['credits']['cast'] as List)
          .map((cast) => Cast.fromJson(cast))
          .toList();
    }

    // Handle different ID types (int, String, or null)
    int movieId = 0;
    if (json['id'] != null) {
      if (json['id'] is int) {
        movieId = json['id'];
      } else if (json['id'] is String) {
        movieId = int.tryParse(json['id']) ?? json['id'].hashCode.abs();
      }
    }

    return Movie(
      id: movieId,
      title: json['title'] ?? json['name'] ?? 'Unknown',
      originalTitle: json['original_title'] ?? json['original_name'],
      subtitle: json['tagline'],
      overview: json['overview'],
      posterPath: json['poster_path'],
      backdropPath: json['backdrop_path'],
      trailerUrl: json['trailer_url'],
      genres: genresList,
      releaseDate: json['release_date'] ?? json['first_air_date'],
      runtime: _parseInt(json['runtime']),
      voteAverage: _parseDouble(json['vote_average']) ??
          _parseDouble(json['voteAverage']),
      voteCount: _parseInt(json['vote_count']) ?? _parseInt(json['voteCount']),
      popularity: _parseDouble(json['popularity']),
      ageRating: json['adult'] == true ? '18+' : json['age_rating'] ?? 'PG',
      language: json['original_language'] ?? json['language'],
      country: json['production_countries'] != null &&
              (json['production_countries'] as List).isNotEmpty
          ? json['production_countries'][0]['name']
          : json['country'],
      director: json['director'],
      cast: castList,
      status: MovieStatusExtension.fromString(json['status']),
      isActive: json['isActive'] ?? json['is_active'] ?? true,
      createdAt: json['createdAt'] != null
          ? (json['createdAt'] as Timestamp).toDate()
          : json['created_at'] != null
              ? (json['created_at'] as Timestamp).toDate()
              : null,
      updatedAt: json['updatedAt'] != null
          ? (json['updatedAt'] as Timestamp).toDate()
          : json['updated_at'] != null
              ? (json['updated_at'] as Timestamp).toDate()
              : null,
      isFavorite: isFavorite,
      isHomeBanner: json['isHomeBanner'] ?? json['is_home_banner'] ?? false,
      isSplashBanner:
          json['isSplashBanner'] ?? json['is_splash_banner'] ?? false,
      bannerOrder:
          _parseInt(json['bannerOrder']) ?? _parseInt(json['banner_order']),
    );
  }

  factory Movie.fromFirestore(DocumentSnapshot doc, {bool isFavorite = false}) {
    final data = doc.data() as Map<String, dynamic>;

    // Handle ID conversion properly
    int movieId;
    if (data['id'] != null) {
      if (data['id'] is int) {
        movieId = data['id'];
      } else if (data['id'] is String) {
        movieId = int.tryParse(data['id']) ?? data['id'].hashCode.abs();
      } else {
        movieId = doc.id.hashCode.abs();
      }
    } else {
      movieId = doc.id.hashCode.abs();
    }

    return Movie.fromJson({...data, 'id': movieId}, isFavorite: isFavorite);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'originalTitle': originalTitle,
      'subtitle': subtitle,
      'overview': overview,
      'posterPath': posterPath,
      'backdropPath': backdropPath,
      'trailerUrl': trailerUrl,
      'genres': genres,
      'releaseDate': releaseDate,
      'runtime': runtime,
      'voteAverage': voteAverage,
      'voteCount': voteCount,
      'popularity': popularity,
      'ageRating': ageRating,
      'language': language,
      'country': country,
      'director': director,
      'cast': cast?.map((c) => c.toJson()).toList(),
      'status': status.name,
      'isActive': isActive,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'isFavorite': isFavorite,
      'isHomeBanner': isHomeBanner,
      'isSplashBanner': isSplashBanner,
      'bannerOrder': bannerOrder,

      // Legacy fields for backward compatibility
      'poster_path': posterPath,
      'backdrop_path': backdropPath,
      'release_date': releaseDate,
      'vote_average': voteAverage,
      'age_rating': ageRating,
      'is_favorite': isFavorite,
      'is_home_banner': isHomeBanner,
      'is_splash_banner': isSplashBanner,
      'banner_order': bannerOrder,
    };
  }

  Map<String, dynamic> toFirestore() => toJson();

  Movie copyWith({
    int? id,
    String? title,
    String? originalTitle,
    String? subtitle,
    String? overview,
    String? posterPath,
    String? backdropPath,
    String? trailerUrl,
    List<String>? genres,
    String? releaseDate,
    int? runtime,
    double? voteAverage,
    int? voteCount,
    double? popularity,
    String? ageRating,
    String? language,
    String? country,
    String? director,
    List<Cast>? cast,
    MovieStatus? status,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isFavorite,
    bool? isHomeBanner,
    bool? isSplashBanner,
    int? bannerOrder,
  }) {
    return Movie(
      id: id ?? this.id,
      title: title ?? this.title,
      originalTitle: originalTitle ?? this.originalTitle,
      subtitle: subtitle ?? this.subtitle,
      overview: overview ?? this.overview,
      posterPath: posterPath ?? this.posterPath,
      backdropPath: backdropPath ?? this.backdropPath,
      trailerUrl: trailerUrl ?? this.trailerUrl,
      genres: genres ?? this.genres,
      releaseDate: releaseDate ?? this.releaseDate,
      runtime: runtime ?? this.runtime,
      voteAverage: voteAverage ?? this.voteAverage,
      voteCount: voteCount ?? this.voteCount,
      popularity: popularity ?? this.popularity,
      ageRating: ageRating ?? this.ageRating,
      language: language ?? this.language,
      country: country ?? this.country,
      director: director ?? this.director,
      cast: cast ?? this.cast,
      status: status ?? this.status,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isFavorite: isFavorite ?? this.isFavorite,
      isHomeBanner: isHomeBanner ?? this.isHomeBanner,
      isSplashBanner: isSplashBanner ?? this.isSplashBanner,
      bannerOrder: bannerOrder ?? this.bannerOrder,
    );
  }

  // Helper methods
  bool get isNowPlaying => status == MovieStatus.nowPlaying;
  bool get isUpcoming => status == MovieStatus.upcoming;
  bool get isEnded => status == MovieStatus.ended;

  String get displayStatus => status.displayName;
  String get displayRuntime => runtime != null ? '${runtime}p' : 'N/A';

  bool get hasTrailer => trailerUrl != null && trailerUrl!.isNotEmpty;
  bool get hasDirector => director != null && director!.isNotEmpty;
  bool get hasCast => cast != null && cast!.isNotEmpty;

  double get ratingPercentage =>
      voteAverage != null ? (voteAverage! * 10) : 0.0;

  String get formattedReleaseDate {
    if (releaseDate == null || releaseDate!.isEmpty) return 'N/A';
    try {
      final date = DateTime.parse(releaseDate!);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return releaseDate!;
    }
  }

  bool get isHighRated => voteAverage != null && voteAverage! >= 7.0;
  bool get isPopular => popularity != null && popularity! > 100.0;
}

class Cast {
  final int id;
  final String name;
  final String character;
  final String? profilePath;

  Cast({
    required this.id,
    required this.name,
    required this.character,
    this.profilePath,
  });

  String get fullProfilePath => profilePath != null
      ? 'https://image.tmdb.org/t/p/w200$profilePath'
      : 'https://via.placeholder.com/200x300?text=No+Image';

  factory Cast.fromJson(Map<String, dynamic> json) {
    return Cast(
      id: json['id'] is int
          ? json['id']
          : int.tryParse(json['id']?.toString() ?? '0') ?? 0,
      name: json['name'] ?? 'Unknown',
      character: json['character'] ?? 'Unknown',
      profilePath: json['profile_path'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'character': character,
      'profile_path': profilePath,
    };
  }
}
