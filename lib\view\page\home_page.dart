import 'dart:async';
import 'dart:developer' as developer;

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../controllers/movie_controller.dart';
import '../../models/movie_model.dart';
import 'movie_detail_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final MovieController _movieController = Get.find<MovieController>();

  // Auto-scroll timer and controller for genre list
  Timer? _autoScrollTimer;
  final ScrollController _genreScrollController = ScrollController();

  // Genre list for auto-scrolling
  final List<Map<String, dynamic>> menuList = [
    {'icon': Icons.local_fire_department, 'title': '<PERSON>ành động'},
    {'icon': Icons.sentiment_satisfied_alt, 'title': 'Hài'},
    {'icon': Icons.favorite, 'title': '<PERSON><PERSON><PERSON> cảm'},
    {'icon': Icons.psychology, 'title': 'Kinh dị'},
    {'icon': Icons.auto_awesome, 'title': 'Viễn tưởng'},
    {'icon': Icons.family_restroom, 'title': 'Gia đình'},
  ];

  @override
  void initState() {
    super.initState();

    // Initialize movie data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _movieController.fetchHomeBannerMovies();
    });

    // Start auto-scroll for genre list
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startAutoScroll();
    });

    // Add listener for movie banners
    ever(_movieController.homeBannerMovies, (List<Movie> movies) {
      if (movies.isEmpty &&
          !_movieController.isLoadingFirebaseMovies.value &&
          mounted) {
        developer.log(
            'HomePage: Movie banners empty, retrying fetch after delay',
            name: 'HomePage');
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted && _movieController.homeBannerMovies.isEmpty) {
            _movieController.fetchHomeBannerMovies();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _genreScrollController.dispose();
    super.dispose();
  }

  void _startAutoScroll() {
    const duration = Duration(seconds: 15);
    _autoScrollTimer = Timer.periodic(duration, (timer) {
      if (_genreScrollController.hasClients) {
        final currentPosition = _genreScrollController.offset;
        final maxScrollExtent = _genreScrollController.position.maxScrollExtent;

        if (currentPosition >= maxScrollExtent) {
          _genreScrollController.animateTo(
            0,
            duration: const Duration(seconds: 1),
            curve: Curves.easeInOut,
          );
        } else {
          _genreScrollController.animateTo(
            currentPosition + 200,
            duration: const Duration(seconds: 1),
            curve: Curves.easeInOut,
          );
        }
      }
    });
  }

  Widget _buildAutoScrollingGenreList() {
    final extendedList = [...menuList, ...menuList];

    return ListView.builder(
      controller: _genreScrollController,
      scrollDirection: Axis.horizontal,
      itemCount: extendedList.length,
      itemBuilder: (context, index) {
        final item = extendedList[index];
        return MenuBtns(
          title: item['title'],
          icon: item['icon'] as IconData,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final searchTextController = TextEditingController();
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xff2B5876),
              Color(0xff4E4376),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Section
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Welcome note
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: '${'hello'.tr},',
                                  style: GoogleFonts.mulish(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w700,
                                    color: Colors.white,
                                  ),
                                ),
                                TextSpan(
                                  text: ' Daizy',
                                  style: GoogleFonts.mulish(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          IconButton(
                            onPressed: () {
                              Get.toNamed('/realtime_notifications');
                            },
                            icon: const Icon(
                              Icons.notifications_outlined,
                              color: Colors.white,
                            ),
                          )
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Search Box
                      TextField(
                        controller: searchTextController,
                        style: GoogleFonts.mulish(
                          color: Colors.white,
                          fontSize: 16,
                        ),
                        decoration: InputDecoration(
                          hintText: 'search_hint'.tr,
                          hintStyle: GoogleFonts.mulish(
                            color: Colors.white54,
                            fontSize: 16,
                          ),
                          prefixIcon: const Icon(
                            Icons.search_outlined,
                            color: Colors.white54,
                          ),
                          suffixIcon: IconButton(
                            onPressed: () {
                              final query = searchTextController.text.trim();
                              if (query.isNotEmpty) {
                                Get.toNamed('/search',
                                    parameters: {'query': query});
                              }
                            },
                            icon: const Icon(
                              Icons.arrow_forward,
                              color: Colors.white60,
                            ),
                          ),
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.1),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),

                      // Banner Movies Section Title
                      Text(
                        'Phim nổi bật',
                        style: GoogleFonts.mulish(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),

                // Movie Banners Section
                Obx(() {
                  final movieBanners = _movieController.homeBannerMovies;
                  final movieErrorMsg = _movieController.errorMessage.value;

                  if (_movieController.isLoadingFirebaseMovies.value) {
                    return const SizedBox(
                      height: 200,
                      child: Center(
                        child: CircularProgressIndicator(color: Colors.amber),
                      ),
                    );
                  }

                  if (movieBanners.isEmpty) {
                    return SizedBox(
                      height: 200,
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.movie_outlined,
                              color: Colors.white54,
                              size: 48,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Không có banner phim',
                              style: GoogleFonts.mulish(
                                color: Colors.white70,
                                fontSize: 16,
                              ),
                            ),
                            if (movieErrorMsg.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Text(
                                  'Lỗi: $movieErrorMsg',
                                  style: GoogleFonts.mulish(
                                    color: Colors.red[300],
                                    fontSize: 12,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  }

                  // Display movie banners using backdrop images
                  return CarouselSlider.builder(
                    options: CarouselOptions(
                      height: 200,
                      autoPlay: true,
                      enlargeCenterPage: true,
                      autoPlayInterval: const Duration(seconds: 5),
                      autoPlayAnimationDuration: const Duration(seconds: 1),
                      viewportFraction: 0.85,
                    ),
                    itemCount: movieBanners.length,
                    itemBuilder: (_, index, realIndex) {
                      final movie = movieBanners[index];
                      return GestureDetector(
                        onTap: () {
                          Get.to(() => MovieDetailsPage(movieId: movie.id));
                        },
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            image: DecorationImage(
                              image: NetworkImage(movie.fullBackdropPath),
                              fit: BoxFit.cover,
                            ),
                          ),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(15),
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.transparent,
                                  Colors.black.withOpacity(0.7),
                                ],
                              ),
                            ),
                            child: Align(
                              alignment: Alignment.bottomLeft,
                              child: Padding(
                                padding: const EdgeInsets.all(16),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      movie.title,
                                      style: GoogleFonts.mulish(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    if (movie.voteAverage != null)
                                      Row(
                                        children: [
                                          const Icon(
                                            Icons.star,
                                            color: Colors.amber,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            movie.rating,
                                            style: GoogleFonts.mulish(
                                              fontSize: 14,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                }),

                const SizedBox(height: 30),

                // Genre List Section
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Text(
                    'Thể loại phim',
                    style: GoogleFonts.mulish(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 15),
                SizedBox(
                  height: 100,
                  child: _buildAutoScrollingGenreList(),
                ),
                const SizedBox(height: 30),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class MenuBtns extends StatelessWidget {
  const MenuBtns({
    required this.title,
    required this.icon,
    Key? key,
  }) : super(key: key);

  final String title;
  final IconData icon;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(5),
      height: 80,
      width: 70,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0x1EA6A1E0),
            Color(0x1EA1F3FE),
          ],
        ),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: Colors.white,
            size: 30,
          ),
          Text(
            title,
            style: GoogleFonts.poppins(
              textStyle: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w200,
              ),
            ),
          )
        ],
      ),
    );
  }
}
