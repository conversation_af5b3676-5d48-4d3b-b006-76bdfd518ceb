import 'dart:async';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../controllers/banner_controller.dart';
import '../../controllers/movie_controller.dart';
import '../../models/banner_model.dart';
import '../../models/movie_model.dart';

class SplashScreen extends StatefulWidget {
  final Widget nextScreen;

  const SplashScreen({
    Key? key,
    required this.nextScreen,
  }) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  // Banner controller
  final BannerController _bannerController = Get.find<BannerController>();
  final MovieController _movieController = Get.find<MovieController>();

  // Animation controller for the app title
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Current banner index for showing the corresponding movie title
  final RxInt _currentBannerIndex = 0.obs;

  @override
  void initState() {
    super.initState();

    // Fetch splash banners
    print('SplashScreen: Initializing and fetching splash banners');
    _bannerController.fetchActiveBanners(type: BannerType.splash);
    _movieController.fetchSplashBannerMovies();

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    );

    // Create fade-in animation
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn,
      ),
    );

    // Start animation
    _animationController.forward();

    // Navigate to the next screen after 8 seconds to give users time to see the banners
    Timer(const Duration(seconds: 8), () {
      Get.off(() => widget.nextScreen, transition: Transition.fadeIn);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          height: MediaQuery.of(context).size.height,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xff2B5876),
                Color(0xff4E4376),
              ],
            ),
          ),
          child: Stack(
            children: [
              // Movie banners carousel
              Positioned.fill(
                child: Obx(() {
                  final splashBanners = _bannerController.splashBanners;
                  final movieBanners = _movieController.splashBannerMovies;

                  if (_bannerController.isLoading.value ||
                      _movieController.isLoadingFirebaseMovies.value) {
                    return const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    );
                  }

                  // Prioritize movie banners over regular banners
                  final displayBanners =
                      movieBanners.isNotEmpty ? movieBanners : null;
                  final displayRegularBanners =
                      splashBanners.isNotEmpty ? splashBanners : null;

                  if (displayBanners == null && displayRegularBanners == null) {
                    return Center(
                      child: Text(
                        'No banners available',
                        style: GoogleFonts.mulish(
                          fontSize: 18,
                          color: Colors.white,
                        ),
                      ),
                    );
                  }

                  // Use movie banners if available, otherwise use regular banners
                  if (displayBanners != null) {
                    // Display movie banners
                    return CarouselSlider.builder(
                      options: CarouselOptions(
                        height: MediaQuery.of(context).size.height,
                        viewportFraction: 1.0,
                        autoPlay: true,
                        enlargeCenterPage: false,
                        autoPlayInterval: const Duration(seconds: 2),
                        autoPlayAnimationDuration:
                            const Duration(milliseconds: 800),
                        autoPlayCurve: Curves.fastOutSlowIn,
                        onPageChanged: (index, reason) {
                          // Update the current banner index
                          _currentBannerIndex.value = index;
                        },
                      ),
                      itemCount: displayBanners.length,
                      itemBuilder: (context, index, realIndex) {
                        final movie = displayBanners[index];
                        return Container(
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: NetworkImage(movie.fullBackdropPath),
                              fit: BoxFit.cover,
                              colorFilter: ColorFilter.mode(
                                Colors.black.withOpacity(0.6),
                                BlendMode.darken,
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  } else {
                    // Display regular banners
                    return CarouselSlider.builder(
                      options: CarouselOptions(
                        height: MediaQuery.of(context).size.height,
                        viewportFraction: 1.0,
                        autoPlay: true,
                        enlargeCenterPage: false,
                        autoPlayInterval: const Duration(seconds: 2),
                        autoPlayAnimationDuration:
                            const Duration(milliseconds: 800),
                        autoPlayCurve: Curves.fastOutSlowIn,
                        onPageChanged: (index, reason) {
                          // Update the current banner index
                          _currentBannerIndex.value = index;
                        },
                      ),
                      itemCount: displayRegularBanners!.length,
                      itemBuilder: (context, index, realIndex) {
                        final banner = displayRegularBanners[index];
                        return Container(
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: NetworkImage(banner.imageUrl),
                              fit: BoxFit.cover,
                              colorFilter: ColorFilter.mode(
                                Colors.black.withOpacity(0.6),
                                BlendMode.darken,
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  }
                }),
              ),

              // Overlay gradient for better text visibility
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        const Color(0xff2B5876).withOpacity(0.3),
                        const Color(0xff4E4376).withOpacity(0.7),
                      ],
                    ),
                  ),
                ),
              ),

              // App title and tagline with animation
              Center(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Movie Finder',
                        style: GoogleFonts.mulish(
                          fontSize: 48,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              blurRadius: 10.0,
                              color: Colors.black.withOpacity(0.5),
                              offset: const Offset(2.0, 2.0),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Discover your next favorite movie',
                        style: GoogleFonts.mulish(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: Colors.white.withOpacity(0.9),
                          shadows: [
                            Shadow(
                              blurRadius: 8.0,
                              color: Colors.black.withOpacity(0.5),
                              offset: const Offset(1.0, 1.0),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 40),
                      // Show current movie title
                      Obx(() {
                        final splashBanners = _bannerController.splashBanners;
                        final movieBanners =
                            _movieController.splashBannerMovies;

                        // Prioritize movie banners
                        final displayBanners =
                            movieBanners.isNotEmpty ? movieBanners : null;
                        final displayRegularBanners =
                            splashBanners.isNotEmpty ? splashBanners : null;

                        if (displayBanners == null &&
                            displayRegularBanners == null) {
                          return const SizedBox.shrink();
                        }

                        String title;
                        if (displayBanners != null) {
                          final index =
                              _currentBannerIndex.value % displayBanners.length;
                          title = displayBanners[index].title;
                        } else {
                          final index = _currentBannerIndex.value %
                              displayRegularBanners!.length;
                          title = displayRegularBanners[index].title;
                        }

                        return AnimatedSwitcher(
                          duration: const Duration(milliseconds: 500),
                          child: Text(
                            title,
                            key: ValueKey<int>(_currentBannerIndex.value),
                            style: GoogleFonts.mulish(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.amber,
                              shadows: [
                                Shadow(
                                  blurRadius: 8.0,
                                  color: Colors.black.withOpacity(0.7),
                                  offset: const Offset(1.0, 1.0),
                                ),
                              ],
                            ),
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),

              // Loading indicator at the bottom
              Positioned(
                bottom: 50,
                left: 0,
                right: 0,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Column(
                    children: [
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                      const SizedBox(height: 20),
                      Obx(() {
                        final splashBanners = _bannerController.splashBanners;
                        final movieBanners =
                            _movieController.splashBannerMovies;

                        // Prioritize movie banners
                        final displayBanners =
                            movieBanners.isNotEmpty ? movieBanners : null;
                        final displayRegularBanners =
                            splashBanners.isNotEmpty ? splashBanners : null;

                        if (displayBanners == null &&
                            displayRegularBanners == null) {
                          return const SizedBox.shrink();
                        }

                        String title;
                        if (displayBanners != null) {
                          final index =
                              _currentBannerIndex.value % displayBanners.length;
                          title = displayBanners[index].title;
                        } else {
                          final index = _currentBannerIndex.value %
                              displayRegularBanners!.length;
                          title = displayRegularBanners[index].title;
                        }

                        return Text(
                          'Now showing: $title',
                          style: GoogleFonts.mulish(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        );
                      }),
                      const SizedBox(height: 8),
                      Text(
                        'Loading amazing movies for you...',
                        style: GoogleFonts.mulish(
                          fontSize: 14,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Skip button
              Positioned(
                top: 50,
                right: 20,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: TextButton(
                    onPressed: () {
                      // Skip the splash screen and go directly to the next screen
                      Get.off(() => widget.nextScreen,
                          transition: Transition.fadeIn);
                    },
                    style: TextButton.styleFrom(
                      backgroundColor: Colors.black.withOpacity(0.3),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    child: Text(
                      'Skip',
                      style: GoogleFonts.mulish(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
